// PDF.js 配置
import * as pdfjsLib from "pdfjs-dist";
import pdfjsWorker from "pdfjs-dist/build/pdf.worker.js?url";
pdfjsLib.GlobalWorkerOptions.workerSrc = pdfjsWorker;

// PDF 阅读器 Alpine 组件
document.addEventListener("alpine:init", () => {
    Alpine.data("pdfViewer", (url) => ({
        // 状态管理
        loading: true,
        error: false,
        currentPage: 1,
        totalPages: 0,
        pdfDoc: null,
        canvas: null,
        context: null,

        // 初始化
        init() {
            this.canvas = this.$refs.canvas;
            this.context = this.canvas.getContext("2d");
        },

        // 加载 PDF
        async loadPdf() {
            if (!url) {
                this.error = true;
                this.loading = false;
                return;
            }

            try {
                this.loading = true;
                this.error = false;

                const loadingTask = pdfjsLib.getDocument(url);
                this.pdfDoc = await loadingTask.promise;
                this.totalPages = this.pdfDoc.numPages;

                await this.renderPage(1);
            } catch (err) {
                console.error("PDF 加载失败:", err);
                this.error = true;
            } finally {
                this.loading = false;
            }
        },

        // 渲染指定页面
        async renderPage(pageNum) {
            if (!this.pdfDoc || pageNum < 1 || pageNum > this.totalPages) {
                return;
            }

            try {
                const page = await this.pdfDoc.getPage(pageNum);
                const containerWidth = this.canvas.parentElement.clientWidth;
                const containerHeight = this.canvas.parentElement.clientHeight;

                // 获取页面原始尺寸
                const viewport = page.getViewport({ scale: 1.0 });

                // 计算缩放比例以适应容器
                const scaleX = containerWidth / viewport.width;
                const scaleY = containerHeight / viewport.height;
                const scale = Math.min(scaleX, scaleY);

                // 应用缩放
                const scaledViewport = page.getViewport({ scale });

                // 设置画布尺寸
                this.canvas.width = scaledViewport.width;
                this.canvas.height = scaledViewport.height;

                // 清除画布
                this.context.clearRect(0, 0, this.canvas.width, this.canvas.height);

                // 渲染页面
                const renderContext = {
                    canvasContext: this.context,
                    viewport: scaledViewport,
                };

                await page.render(renderContext).promise;
                this.currentPage = pageNum;
            } catch (err) {
                console.error("页面渲染失败:", err);
                this.error = true;
            }
        },

        // 上一页
        async prevPage() {
            if (this.currentPage > 1) {
                await this.renderPage(this.currentPage - 1);
            }
        },

        // 下一页
        async nextPage() {
            if (this.currentPage < this.totalPages) {
                await this.renderPage(this.currentPage + 1);
            }
        },

        // URL 变化时重新加载
        async $watch() {
            this.$watch("url", async (newUrl) => {
                if (newUrl !== url) {
                    url = newUrl;
                    this.currentPage = 1;
                    await this.loadPdf();
                }
            });
        },
    }));
});
