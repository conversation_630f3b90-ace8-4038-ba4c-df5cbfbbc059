@props([
    "url",
])

<div x-data="pdfViewer('{{ $url }}')" x-init="loadPdf()" class="relative aspect-video max-h-[calc(100vh-56px)] w-full border-b bg-zinc-50">
    <!-- 加载状态 -->
    <div x-show="loading" class="flex h-full items-center justify-center">
        <div class="text-zinc-600">加载中...</div>
    </div>

    <!-- 错误状态 -->
    <div x-show="error" class="flex h-full items-center justify-center">
        <div class="text-red-600">PDF 加载失败</div>
    </div>

    <!-- PDF 画布 -->
    <canvas x-ref="canvas" x-show="!loading && !error" class="h-full w-full object-contain"></canvas>

    <!-- 翻页控制 -->
    <div
        x-show="! loading && ! error && totalPages > 1"
        class="absolute bottom-8 left-1/2 flex -translate-x-1/2 items-center gap-x-2 rounded-lg bg-zinc-800/30 p-1 text-sm text-white"
    >
        <flux:button x-show="currentPage > 1" @click="prevPage()" square variant="ghost" size="sm">
            <flux:icon.chevron-left variant="mini" class="text-white" />
        </flux:button>

        <span x-text="`${currentPage} / ${totalPages}`"></span>

        <flux:button x-show="currentPage < totalPages" @click="nextPage()" square variant="ghost" size="sm">
            <flux:icon.chevron-right variant="mini" class="text-white" />
        </flux:button>
    </div>
</div>
