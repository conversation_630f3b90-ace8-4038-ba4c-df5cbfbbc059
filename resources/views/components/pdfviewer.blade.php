@props([
    "url",
])

<div x-data="pdfViewer('{{ $url }}')" x-init="loadPdf()" class="h-full w-full">
    <!-- Loading State -->
    <div x-show="loading" class="flex h-64 items-center justify-center">
        <div class="text-gray-600">加载中...</div>
    </div>

    <!-- PDF Container -->
    <div x-show="!loading && !error" class="flex h-full w-full flex-col">
        <!-- Canvas Container -->
        <div class="w-full flex-1 overflow-hidden">
            <canvas x-ref="canvas" class="h-full w-full object-contain" style="display: block; max-width: 100%; max-height: 100%"></canvas>
        </div>

        <!-- Navigation Controls -->
        <div x-show="totalPages > 1" class="flex items-center justify-center gap-4 bg-gray-50 p-4">
            <button x-show="currentPage > 1" @click="prevPage()" class="rounded bg-blue-500 px-4 py-2 text-white transition-colors hover:bg-blue-600">
                上一页
            </button>

            <span class="text-gray-700">
                第
                <span x-text="currentPage"></span>
                页，共
                <span x-text="totalPages"></span>
                页
            </span>

            <button
                x-show="currentPage < totalPages"
                @click="nextPage()"
                class="rounded bg-blue-500 px-4 py-2 text-white transition-colors hover:bg-blue-600"
            >
                下一页
            </button>
        </div>
    </div>

    <!-- Error State -->
    <div x-show="error" class="flex h-64 items-center justify-center">
        <div class="text-red-600">加载失败，请重试</div>
    </div>
</div>
